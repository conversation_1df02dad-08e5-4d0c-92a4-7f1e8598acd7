import os
import sys
import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider, CheckButtons
import argparse
from pathlib import Path
import hydra
from omegaconf import OmegaConf
import time

from dataprocess.volume import Volume
from predict.post_processor import GaussianBlurProcessor
from utils.misc import create_gaussian_kernel_3d


def process_volume_with_gaussian_blur(
    input_path,
    output_path=None,
    sigma=1.0,
    kernel_size=None,
    device=None,
    visualize=False,
    save_comparison=False,
):
    """
    加载指定的volume并进行高斯模糊操作

    Args:
        input_path (str): 输入volume文件路径
        output_path (str, optional): 输出volume文件路径，如果为None则不保存
        sigma (float or tuple): 高斯核的标准差，可以是单个浮点数或三元组 (sigma_z, sigma_y, sigma_x)
        kernel_size (int or tuple, optional): 高斯核的大小，可以是单个整数或三元组 (size_z, size_y, size_x)
        device (str, optional): 计算设备，如果为None则自动选择
        visualize (bool): 是否可视化原始和模糊后的volume
        save_comparison (bool): 是否保存原始和模糊后的volume对比图

    Returns:
        Volume: 模糊后的Volume对象
    """
    print(f"加载volume: {input_path}")
    start_time = time.time()

    # 加载volume
    volume = Volume(input_path)
    volume.load()

    print(f"Volume加载完成，形状: {volume.volume.shape}, 类型: {volume.volume.dtype}")

    # 创建原始volume的副本
    original_volume = volume.volume.copy()

    volume.volume = volume.volume.astype(np.float32)

    # 创建高斯模糊处理器
    processor = GaussianBlurProcessor()

    # 应用高斯模糊
    print(f"应用高斯模糊 (sigma={sigma}, kernel_size={kernel_size})")
    volume.volume = processor.process(
        volume=volume.volume, sigma=sigma, kernel_size=kernel_size, device=device
    )

    print(f"高斯模糊完成，耗时: {time.time() - start_time:.2f}秒")

    # 保存处理后的volume
    if output_path:
        print(f"保存模糊后的volume到: {output_path}")
        volume.save_volume(output_path)

    # 可视化原始和模糊后的volume
    if visualize:
        visualize_original_and_blurred(original_volume, volume.volume)

    # 保存原始和模糊后的volume对比图
    if save_comparison:
        save_comparison_images(
            original_volume,
            volume.volume,
            output_dir=(
                os.path.dirname(output_path) if output_path else "output/gaussian_blur"
            ),
        )

    return volume


def visualize_original_and_blurred(original_volume, blurred_volume):
    """
    可视化原始和模糊后的volume

    Args:
        original_volume (np.ndarray): 原始volume数据
        blurred_volume (np.ndarray): 模糊后的volume数据
    """
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))
    plt.subplots_adjust(bottom=0.25)

    # 初始显示中间切片
    slice_idx = original_volume.shape[0] // 2

    # 显示原始volume
    im1 = axes[0].imshow(original_volume[slice_idx], cmap="gray")
    axes[0].set_title("原始Volume")

    # 显示模糊后的volume
    im2 = axes[1].imshow(blurred_volume[slice_idx], cmap="gray")
    axes[1].set_title("高斯模糊后Volume")

    # 添加滑块控制切片
    ax_slice = plt.axes([0.25, 0.1, 0.65, 0.03])
    slider = Slider(
        ax_slice, "Slice", 0, original_volume.shape[0] - 1, valinit=slice_idx, valstep=1
    )

    # 添加方向选择
    ax_check = plt.axes([0.05, 0.4, 0.15, 0.15])
    check = CheckButtons(ax_check, ("Z", "Y", "X"), (True, False, False))

    def update(val):
        idx = int(slider.val)

        # 根据选择的方向更新显示
        if check.get_status()[0]:  # Z方向
            im1.set_data(original_volume[idx])
            im2.set_data(blurred_volume[idx])
        elif check.get_status()[1]:  # Y方向
            im1.set_data(original_volume[:, idx, :])
            im2.set_data(blurred_volume[:, idx, :])
        elif check.get_status()[2]:  # X方向
            im1.set_data(original_volume[:, :, idx])
            im2.set_data(blurred_volume[:, :, idx])

        # 更新显示范围
        im1.autoscale()
        im2.autoscale()
        fig.canvas.draw_idle()

    slider.on_changed(update)
    check.on_clicked(update)

    plt.show()


def save_comparison_images(original_volume, blurred_volume, output_dir, num_slices=5):
    """
    保存原始和模糊后的volume对比图

    Args:
        original_volume (np.ndarray): 原始volume数据
        blurred_volume (np.ndarray): 模糊后的volume数据
        output_dir (str): 输出目录
        num_slices (int): 要保存的切片数量
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 计算要保存的切片索引
    depth = original_volume.shape[0]
    slice_indices = np.linspace(0, depth - 1, num_slices, dtype=int)

    # 保存每个切片的对比图
    for i, idx in enumerate(slice_indices):
        fig, axes = plt.subplots(1, 2, figsize=(12, 6))

        # 显示原始volume
        axes[0].imshow(original_volume[idx], cmap="gray")
        axes[0].set_title("原始Volume")

        # 显示模糊后的volume
        axes[1].imshow(blurred_volume[idx], cmap="gray")
        axes[1].set_title("高斯模糊后Volume")

        plt.suptitle(f"Slice {idx}/{depth-1}")
        plt.tight_layout()

        # 保存图像
        output_path = os.path.join(output_dir, f"comparison_slice_{idx}.png")
        plt.savefig(output_path, dpi=150)
        plt.close()

    print(f"已保存{num_slices}张对比图到: {output_dir}")


@hydra.main(config_path="config", config_name="config", version_base=None)
def main(cfg):
    """
    主函数，使用hydra配置
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="对Volume进行高斯模糊处理")
    parser.add_argument("--input", type=str, help="输入volume文件路径")
    parser.add_argument("--output", type=str, default=None, help="输出volume文件路径")
    parser.add_argument("--sigma", type=float, default=1.0, help="高斯核的标准差")
    parser.add_argument("--kernel_size", type=int, default=None, help="高斯核的大小")
    parser.add_argument("--device", type=str, default=None, help="计算设备 (cpu或cuda)")
    parser.add_argument(
        "--visualize", action="store_true", help="可视化原始和模糊后的volume"
    )
    parser.add_argument(
        "--save_comparison", action="store_true", help="保存原始和模糊后的volume对比图"
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 如果没有提供输入路径，使用配置中的默认路径
    input_path = args.input
    if input_path is None:
        input_path = os.path.join(cfg.output_root, "avg/em_s0_233_avg.zst")

    # 如果没有提供输出路径，但需要保存结果，则生成默认输出路径
    output_path = args.output
    if output_path is None and (not args.visualize or args.save_comparison):
        input_filename = os.path.basename(input_path)
        output_filename = f"{os.path.splitext(input_filename)[0]}_blurred.zst"
        output_path = os.path.join(cfg.output_root, "gaussian_blur", output_filename)
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 处理volume
    process_volume_with_gaussian_blur(
        input_path=input_path,
        output_path=output_path,
        sigma=args.sigma,
        kernel_size=args.kernel_size,
        device=args.device,
        visualize=args.visualize,
        save_comparison=args.save_comparison,
    )


if __name__ == "__main__":
    main()
