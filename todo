Check frame 37
纠正memory机制中没有考虑空间连续性的问题
目前mask的格式在uint8、bool、float之间转换，需要统一 nned check
position encoding
volume_to_frames不用pillow，直接用numpy

目前的slice一共6859 per volume

输出为什么会用低分辨率的mask呢？
ref_mask 存的时候用bf16
需不需要加当前帧的mem信息

明度规格化-对每个单一数据集/所有数据集


82

今日：测提升，bf16，keep_mem

torch_em, elf(有mutex_watershed) 两个库

56934/8, 42714/8, 11259/8 = 7116, 5339, 1407


目前：
unet全监督：0.9325
fewshot+unet：0.9244
fewshot+unet+微调sam：0.8956
fewshot+unet+无微调sam：0.9096

fewshot avg 无微调：
"average": {
    "dice": 0.9384837323047561,
    "iou": 0.884316446011141,
    "precision": 0.9671865723239483,
    "recall": 0.9116423860559136,
    "accuracy": 0.884316446011141,
    "processing_time": 23.9780912399292
  }

看看模型初始化方法
高分辨率直出（in已改，fs试试？）
针对in的mem0 不行

看看mv to mv怎么吹