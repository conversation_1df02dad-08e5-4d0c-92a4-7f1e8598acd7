import os
import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Any, Union
from dataprocess.volume import Volume
from tqdm import tqdm


class VolumeDataLoader:
    """
    体积数据加载器基类，负责基本的数据加载和预处理
    """

    def __init__(self, config_manager=None):
        """
        初始化数据加载器

        Args:
            config_manager: 配置管理器
        """
        self.config = config_manager
        self.volumes = {}
        self.masks = {}

    def load_volume(
        self, volume_path: Union[str, Volume], to_memory: bool = False
    ) -> Volume:
        """
        加载体积数据

        Args:
            volume_path: 体积数据路径
            to_memory: 是否保存到内存中

        Returns:
            加载的体积数据
        """
        if isinstance(volume_path, Volume):
            volume = volume_path
        else:
            if volume_path in self.volumes and to_memory:
                return self.volumes[volume_path]

            volume = Volume(volume_path)
            volume.load()

            if to_memory:
                self.volumes[volume_path] = volume

        return volume

    def load_mask(
        self, mask_path: str, to_memory: bool = False, process_func=None
    ) -> Volume:
        """
        加载mask数据

        Args:
            mask_path: mask数据路径
            to_memory: 是否保存到内存中
            process_func: 可选的处理函数，用于对加载的mask进行处理

        Returns:
            加载的mask数据
        """
        if mask_path in self.masks and to_memory:
            return self.masks[mask_path]

        mask = Volume(mask_path)
        mask.load()

        # 如果提供了处理函数，则调用它
        if process_func is not None:
            process_func(mask)

        if to_memory:
            self.masks[mask_path] = mask

        return mask

    def preprocess_volume(
        self, volume: Volume, target_size: Tuple[int, int, int] = (1024, 1024, 1024)
    ) -> Volume:
        """
        预处理体积数据

        Args:
            volume: 体积数据
            target_size: 目标大小

        Returns:
            预处理后的体积数据
        """
        volume.scale_volume_to(target_size)
        return volume

    def get_frames(
        self, volume: Volume, direction: str = "z"
    ) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        获取体积的帧

        Args:
            volume: 体积数据
            direction: 方向，可以是 "x", "y", "z"

        Returns:
            体积的帧和帧信息
        """
        vf_args = volume.volume_to_frames(direction=direction, use_pil=False)
        return volume.frames, vf_args

    def clear_cache(self) -> None:
        """
        清除缓存的数据
        """
        self.volumes.clear()
        self.masks.clear()

    def get_prediction_datasets(
        self,
        volume_dir: str,
        masks_dir: Optional[str] = None,
        ref_masks_dir: Optional[str] = None,
    ) -> List[Dict[str, str]]:
        """
        获取预测数据集

        Args:
            volume_dir: 体积数据目录
            masks_dir: mask数据目录
            ref_masks_dir: 参考mask数据目录

        Returns:
            预测数据集列表
        """
        datasets = []

        # 获取体积文件
        volume_files = sorted([f for f in os.listdir(volume_dir) if f.endswith(".zst")])
        volume_paths = [os.path.join(volume_dir, f) for f in volume_files]

        # 如果提供了mask目录，获取mask文件
        masks_paths = []
        if masks_dir is not None:
            mask_files = sorted(
                [f for f in os.listdir(masks_dir) if f.endswith(".zst")]
            )
            masks_paths = [os.path.join(masks_dir, f) for f in mask_files]

            # 检查文件数量是否匹配
            if len(volume_paths) != len(masks_paths):
                raise ValueError(
                    f"Number of volume files ({len(volume_paths)}) does not match number of mask files ({len(masks_paths)})"
                )

        # 如果提供了参考mask目录，获取参考mask文件
        ref_masks_paths = []
        if ref_masks_dir is not None:
            ref_mask_files = sorted(
                [f for f in os.listdir(ref_masks_dir) if f.endswith(".zst")]
            )
            ref_masks_paths = [os.path.join(ref_masks_dir, f) for f in ref_mask_files]

            # 检查文件数量是否匹配
            if len(volume_paths) != len(ref_masks_paths):
                raise ValueError(
                    f"Number of volume files ({len(volume_paths)}) does not match number of reference mask files ({len(ref_masks_paths)})"
                )

        # 创建数据集条目
        for i, volume_path in enumerate(volume_paths):
            # 创建数据集条目
            dataset_entry = {
                "volume_path": volume_path,
            }

            # 添加可选字段（如果可用）
            if masks_paths:
                dataset_entry["masks_path"] = masks_paths[i]
            if ref_masks_paths:
                dataset_entry["ref_masks_path"] = ref_masks_paths[i]

            datasets.append(dataset_entry)

        return datasets


class FewshotDataLoader(VolumeDataLoader):
    """
    Few-shot数据加载器，专门处理few-shot模式的数据加载
    """

    def __init__(self, config_manager=None):
        """
        初始化few-shot数据加载器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)

    def load_mask(self, mask_path: str, to_memory: bool = False) -> Volume:
        """
        加载mask数据，并转换为二进制布尔值

        Args:
            mask_path: mask数据路径
            to_memory: 是否保存到内存中

        Returns:
            加载的mask数据
        """
        return super().load_mask(
            mask_path, to_memory, process_func=lambda m: m.volume_to_binary_bool()
        )

    def get_few_masks(
        self, masks: Volume, label_start: int, label_stride: int
    ) -> Dict[int, np.ndarray]:
        """
        获取few-shot mask

        Args:
            masks: mask数据
            label_start: 标签起始位置
            label_stride: 标签步长

        Returns:
            few-shot mask字典{frame_idx: mask}
        """
        real_start = label_start % label_stride
        few_masks = masks.frames[real_start::label_stride]

        few_masks_dict = {}
        for i, mask in enumerate(few_masks):
            frame_idx = real_start + i * label_stride
            few_masks_dict[frame_idx] = mask

        return few_masks_dict


class InferenceDataLoader(VolumeDataLoader):
    """
    推理数据加载器，专门处理推理模式的数据加载
    """

    def __init__(self, config_manager=None):
        """
        初始化推理数据加载器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)

    def load_ref_mask(self, ref_mask_path: str, to_memory: bool = False) -> Volume:
        """
        加载参考mask数据

        Args:
            ref_mask_path: 参考mask数据路径
            to_memory: 是否保存到内存中

        Returns:
            加载的参考mask数据
        """
        return super().load_mask(ref_mask_path, to_memory)

    def prepare_volume_for_unet(self, volume: Volume) -> np.ndarray:
        """
        为UNet模型准备体积数据

        Args:
            volume: 体积数据

        Returns:
            预处理后的体积数据
        """
        # 缩放体积到UNet所需的大小
        volume_downsampled = volume.scale_volume_to((512, 512, 512), copy=True)
        return volume_downsampled
