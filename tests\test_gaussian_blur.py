import os
import sys
import numpy as np
import torch
import matplotlib.pyplot as plt
from pathlib import Path
import pytest

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.misc import create_gaussian_kernel_3d
from predict.post_processor import GaussianBlurProcessor


class TestGaussianKernel3D:
    """测试3D高斯核生成函数"""

    def test_kernel_shape(self):
        """测试生成的核形状是否正确"""
        # 测试单一sigma值
        sigma = 1.0
        kernel_size = 7
        kernel = create_gaussian_kernel_3d(sigma, kernel_size)
        
        # 检查核的形状
        assert kernel.shape == (1, 1, kernel_size, kernel_size, kernel_size)
        
        # 测试不同的sigma值
        sigma = (1.0, 2.0, 3.0)
        kernel_size = (5, 7, 9)
        kernel = create_gaussian_kernel_3d(sigma, kernel_size)
        
        # 检查核的形状
        assert kernel.shape == (1, 1, kernel_size[0], kernel_size[1], kernel_size[2])

    def test_kernel_normalization(self):
        """测试生成的核是否归一化（和为1）"""
        sigma = 1.5
        kernel = create_gaussian_kernel_3d(sigma)
        
        # 检查核的和是否接近1
        assert np.isclose(kernel.sum().item(), 1.0, rtol=1e-5)

    def test_kernel_symmetry(self):
        """测试生成的核是否对称"""
        sigma = 1.0
        kernel_size = 7
        kernel = create_gaussian_kernel_3d(sigma, kernel_size)
        
        # 提取核的数据
        k = kernel.squeeze().cpu().numpy()
        
        # 检查沿各个维度的对称性
        # Z轴对称
        assert np.allclose(k[0], k[-1])
        assert np.allclose(k[1], k[-2])
        
        # Y轴对称
        assert np.allclose(k[:, 0], k[:, -1])
        assert np.allclose(k[:, 1], k[:, -2])
        
        # X轴对称
        assert np.allclose(k[:, :, 0], k[:, :, -1])
        assert np.allclose(k[:, :, 1], k[:, :, -2])

    def test_kernel_peak(self):
        """测试核的峰值是否在中心"""
        sigma = 1.0
        kernel_size = 7
        kernel = create_gaussian_kernel_3d(sigma, kernel_size)
        
        # 提取核的数据
        k = kernel.squeeze().cpu().numpy()
        
        # 找到最大值的索引
        max_idx = np.unravel_index(np.argmax(k), k.shape)
        
        # 检查最大值是否在中心
        center = (kernel_size // 2, kernel_size // 2, kernel_size // 2)
        assert max_idx == center

    def test_kernel_device(self):
        """测试核是否在指定的设备上"""
        sigma = 1.0
        
        # 测试CPU
        kernel_cpu = create_gaussian_kernel_3d(sigma, device="cpu")
        assert kernel_cpu.device.type == "cpu"
        
        # 如果有CUDA，测试CUDA
        if torch.cuda.is_available():
            kernel_cuda = create_gaussian_kernel_3d(sigma, device="cuda")
            assert kernel_cuda.device.type == "cuda"


class TestGaussianBlurProcessor:
    """测试高斯模糊后处理器"""

    def test_processor_initialization(self):
        """测试后处理器初始化"""
        processor = GaussianBlurProcessor()
        assert processor is not None

    def test_blur_3d_numpy(self):
        """测试对3D NumPy数组的模糊处理"""
        # 创建一个简单的3D体积数据
        volume = np.zeros((20, 30, 40), dtype=np.float32)
        # 在中心放置一个点源
        volume[10, 15, 20] = 1.0
        
        # 创建处理器并应用模糊
        processor = GaussianBlurProcessor()
        blurred = processor.process(volume, sigma=2.0)
        
        # 检查输出类型和形状
        assert isinstance(blurred, np.ndarray)
        assert blurred.shape == volume.shape
        
        # 检查模糊效果：中心点的值应该小于1，但仍然是最大值
        assert blurred[10, 15, 20] < 1.0
        assert np.unravel_index(np.argmax(blurred), blurred.shape) == (10, 15, 20)
        
        # 检查周围点的值应该大于0（扩散效果）
        assert blurred[9, 15, 20] > 0.0
        assert blurred[11, 15, 20] > 0.0
        assert blurred[10, 14, 20] > 0.0
        assert blurred[10, 16, 20] > 0.0
        assert blurred[10, 15, 19] > 0.0
        assert blurred[10, 15, 21] > 0.0

    def test_blur_3d_torch(self):
        """测试对3D PyTorch张量的模糊处理"""
        # 创建一个简单的3D体积数据
        volume = torch.zeros((20, 30, 40), dtype=torch.float32)
        # 在中心放置一个点源
        volume[10, 15, 20] = 1.0
        
        # 创建处理器并应用模糊
        processor = GaussianBlurProcessor()
        blurred = processor.process(volume, sigma=2.0)
        
        # 检查输出类型和形状
        assert isinstance(blurred, torch.Tensor)
        assert blurred.shape == volume.shape
        
        # 检查模糊效果：中心点的值应该小于1，但仍然是最大值
        assert blurred[10, 15, 20].item() < 1.0
        max_idx = torch.argmax(blurred.view(-1)).item()
        max_idx = np.unravel_index(max_idx, blurred.shape)
        assert max_idx == (10, 15, 20)
        
        # 检查周围点的值应该大于0（扩散效果）
        assert blurred[9, 15, 20].item() > 0.0
        assert blurred[11, 15, 20].item() > 0.0
        assert blurred[10, 14, 20].item() > 0.0
        assert blurred[10, 16, 20].item() > 0.0
        assert blurred[10, 15, 19].item() > 0.0
        assert blurred[10, 15, 21].item() > 0.0

    def test_blur_4d_multichannel(self):
        """测试对4D多通道数据的模糊处理"""
        # 创建一个简单的4D体积数据（2个通道）
        volume = torch.zeros((2, 20, 30, 40), dtype=torch.float32)
        # 在第一个通道的中心放置一个点源
        volume[0, 10, 15, 20] = 1.0
        # 在第二个通道的不同位置放置一个点源
        volume[1, 5, 10, 15] = 1.0
        
        # 创建处理器并应用模糊
        processor = GaussianBlurProcessor()
        blurred = processor.process(volume, sigma=2.0)
        
        # 检查输出类型和形状
        assert isinstance(blurred, torch.Tensor)
        assert blurred.shape == volume.shape
        
        # 检查第一个通道的模糊效果
        assert blurred[0, 10, 15, 20].item() < 1.0
        assert blurred[0, 9, 15, 20].item() > 0.0
        
        # 检查第二个通道的模糊效果
        assert blurred[1, 5, 10, 15].item() < 1.0
        assert blurred[1, 4, 10, 15].item() > 0.0

    def test_different_sigmas(self):
        """测试不同的sigma值对模糊效果的影响"""
        # 创建一个简单的3D体积数据
        volume = np.zeros((20, 30, 40), dtype=np.float32)
        # 在中心放置一个点源
        volume[10, 15, 20] = 1.0
        
        # 创建处理器
        processor = GaussianBlurProcessor()
        
        # 应用不同sigma的模糊
        blurred_small = processor.process(volume, sigma=1.0)
        blurred_medium = processor.process(volume, sigma=2.0)
        blurred_large = processor.process(volume, sigma=3.0)
        
        # 检查不同sigma的扩散效果
        # 较小的sigma应该扩散较少，中心点的值应该较大
        # 较大的sigma应该扩散较多，中心点的值应该较小
        assert blurred_small[10, 15, 20] > blurred_medium[10, 15, 20] > blurred_large[10, 15, 20]
        
        # 检查远离中心的点，较大的sigma应该有更大的值（更广泛的扩散）
        far_point = (5, 7, 9)  # 远离中心的点
        assert blurred_small[far_point] < blurred_medium[far_point] < blurred_large[far_point]


def visualize_gaussian_kernel():
    """可视化3D高斯核（非测试函数，用于演示）"""
    # 创建输出目录
    output_dir = Path("output/gaussian_kernel")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成高斯核
    sigma = 1.5
    kernel_size = 11
    kernel = create_gaussian_kernel_3d(sigma, kernel_size)
    k = kernel.squeeze().cpu().numpy()
    
    # 可视化核的中心切片
    center = kernel_size // 2
    
    # XY平面（Z中心切片）
    plt.figure(figsize=(10, 8))
    plt.imshow(k[center, :, :], cmap='viridis')
    plt.colorbar(label='Value')
    plt.title(f'3D Gaussian Kernel (XY plane, Z={center})')
    plt.savefig(output_dir / 'gaussian_kernel_xy.png')
    
    # XZ平面（Y中心切片）
    plt.figure(figsize=(10, 8))
    plt.imshow(k[:, center, :], cmap='viridis')
    plt.colorbar(label='Value')
    plt.title(f'3D Gaussian Kernel (XZ plane, Y={center})')
    plt.savefig(output_dir / 'gaussian_kernel_xz.png')
    
    # YZ平面（X中心切片）
    plt.figure(figsize=(10, 8))
    plt.imshow(k[:, :, center], cmap='viridis')
    plt.colorbar(label='Value')
    plt.title(f'3D Gaussian Kernel (YZ plane, X={center})')
    plt.savefig(output_dir / 'gaussian_kernel_yz.png')
    
    print(f"Kernel visualizations saved to {output_dir}")


def visualize_gaussian_blur():
    """可视化高斯模糊效果（非测试函数，用于演示）"""
    # 创建输出目录
    output_dir = Path("output/gaussian_blur")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建一个简单的3D体积数据
    volume = np.zeros((30, 30, 30), dtype=np.float32)
    # 在中心放置一个点源
    volume[15, 15, 15] = 1.0
    
    # 创建处理器
    processor = GaussianBlurProcessor()
    
    # 应用不同sigma的模糊
    sigmas = [0.5, 1.0, 2.0, 3.0]
    blurred_volumes = [processor.process(volume, sigma=s) for s in sigmas]
    
    # 可视化原始体积和模糊后的体积
    center = 15
    
    # 原始体积
    plt.figure(figsize=(10, 8))
    plt.imshow(volume[:, :, center], cmap='viridis')
    plt.colorbar(label='Value')
    plt.title('Original Volume (XY plane, center slice)')
    plt.savefig(output_dir / 'original_volume.png')
    
    # 模糊后的体积
    for sigma, blurred in zip(sigmas, blurred_volumes):
        plt.figure(figsize=(10, 8))
        plt.imshow(blurred[:, :, center], cmap='viridis')
        plt.colorbar(label='Value')
        plt.title(f'Blurred Volume (sigma={sigma})')
        plt.savefig(output_dir / f'blurred_volume_sigma_{sigma}.png')
    
    print(f"Blur visualizations saved to {output_dir}")


if __name__ == "__main__":
    # 运行可视化函数
    visualize_gaussian_kernel()
    visualize_gaussian_blur()
    
    # 也可以直接运行测试
    # pytest.main(["-xvs", __file__])
