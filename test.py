import numpy as np
from skimage import morphology
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from scipy import ndimage
import hydra
import os
from dataprocess.volume import Volume
from time import time
from utils.misc import mask_dilation


@hydra.main(config_path="config", config_name="config", version_base=None)
def main(cfg):
    sdf = Volume(
        os.path.join(
            cfg.output_root,
            "em_s0_222_avg.zst",
        )
    )
    sdf.load()
    distance = sdf.volume
    binary_image_3d = distance > 0
    t0 = time()
    binary_image_3d = mask_dilation(binary_image_3d, radius=2)
    t1 = time()
    print("耗时:", t1 - t0)
    print(distance.shape)

    # 2. 寻找局部最大值作为标记 (markers)
    # min_distance 是寻找局部最大值时的最小距离，对于3D图像需要合适的调整
    local_maxi = distance > 10

    t0 = time()
    markers = ndimage.label(local_maxi)[0]
    t1 = time()
    print("耗时:", t1 - t0)

    # 3. 应用 Watershed 算法
    # watershed 函数的第二个参数通常是图像的梯度图或原始图像的负值（以便将局部最小值视为“盆地”）
    # 对于距离变换，通常直接对距离变换的负值应用，因为其局部最大值对应于“山峰”，负值后变为“盆地”
    # t0 = time()
    # distance = distance.astype(np.uint8)
    # t1 = time()
    # print("耗时:", t1 - t0)
    t0 = time()
    labels = watershed(-distance, markers, mask=binary_image_3d)
    t1 = time()
    print("耗时:", t1 - t0)

    # labels 将是分割后的3D图像，每个分割区域有唯一的标签
    mask = Volume(None)
    mask.volume = labels
    mask.save_volume(os.path.join(cfg.output_root, "em_s0_222_watershed.zst"))


if __name__ == "__main__":
    main()
