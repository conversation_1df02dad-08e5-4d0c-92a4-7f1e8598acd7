import os
import numpy as np
from memory_profiler import profile
from dataprocess.volume import Volume
from tqdm import tqdm

input_dir = "F:/dev/CT/3d-seg/datasets/em_s0/test"
output_dir = "F:/dev/CT/3d-seg/datasets/em_s0/test"

for file in tqdm(os.listdir(input_dir)):
    if file.endswith(".zst"):
        volume_path = os.path.join(input_dir, file)
        out_path = os.path.join(output_dir, file)
        volume = Volume(volume_path)
        volume.load()
        volume.volume_to_binary_8bit()
        volume.save_volume(out_path)
