import os
from PIL import Image
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import <PERSON>lider, CheckButtons

# Define paths
image_dir = 'F:/dev/CT/segment-anything-2/utils/jpg_images'
pred_mask_dir = 'F:/dev/CT/segment-anything-2/pred_mask_images'
true_mask_dir = 'F:/dev/CT/segment-anything-2/target_mask_images'

# Load images and masks
images = sorted([os.path.join(image_dir, f) for f in os.listdir(image_dir) if f.endswith('.jpg')])
pred_masks = sorted([os.path.join(pred_mask_dir, f) for f in os.listdir(pred_mask_dir) if f.endswith('.png')])
true_masks = sorted([os.path.join(true_mask_dir, f) for f in os.listdir(true_mask_dir) if f.endswith('.png')])

# Function to overlay masks on image
def overlay_masks(image, pred_mask, true_mask, show_pred, show_true):
    image = np.array(Image.open(image).convert('RGBA'))
    pred_mask = np.array(Image.open(pred_mask).convert('L'))
    true_mask = np.array(Image.open(true_mask).convert('L'))

    # Create RGBA masks with transparency
    pred_rgba = np.zeros((pred_mask.shape[0], pred_mask.shape[1], 4), dtype=np.uint8)
    true_rgba = np.zeros((true_mask.shape[0], true_mask.shape[1], 4), dtype=np.uint8)

    if show_pred:
        pred_rgba[pred_mask > 0] = [255, 0, 0, 128]
    if show_true:
        true_rgba[true_mask > 0] = [0, 0, 255, 128]

    # Blend masks with the image
    blended = Image.alpha_composite(Image.fromarray(image), Image.fromarray(pred_rgba))
    blended = Image.alpha_composite(blended, Image.fromarray(true_rgba))

    return np.array(blended)

# Initial display
fig, ax = plt.subplots()
plt.subplots_adjust(left=0.25, bottom=0.25)
frame_id = 0
total_frames = min(len(images), len(pred_masks), len(true_masks))
show_pred = True
show_true = True
overlay = overlay_masks(images[frame_id], pred_masks[frame_id], true_masks[frame_id], show_pred, show_true)
im = ax.imshow(overlay)

# Slider
ax_slider = plt.axes([0.25, 0.1, 0.65, 0.03])
slider = Slider(ax_slider, 'Frame', 0, len(images) - 1, valinit=0, valstep=1)

# Checkboxes
rax = plt.axes([0.025, 0.5, 0.20, 0.15])
check = CheckButtons(rax, ['Show Pred Mask', 'Show True Mask'], [show_pred, show_true])

# Update function
def update(val):
    frame_id = int(slider.val)
    show_pred = check.get_status()[0]
    show_true = check.get_status()[1]
    overlay = overlay_masks(images[frame_id], pred_masks[frame_id], true_masks[frame_id], show_pred, show_true)
    im.set_data(overlay)
    fig.canvas.draw_idle()

slider.on_changed(update)
check.on_clicked(update)

plt.show()