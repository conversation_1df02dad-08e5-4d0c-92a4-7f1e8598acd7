import os
import hydra
import json
from tqdm import tqdm
from collections import defaultdict
import numpy as np

from dataprocess.volume import Volume


def build_em_dataset_dict(root_dir):
    """
    Traverse the directory structure of the dataset and generate a dictionary
    in the format {dataset_id: [em_path1, em_path2,...]}. It includes datasets
    from both the "train" and "val" directories.]
    """
    dataset_dict = defaultdict(list)

    for split in ["train", "val"]:
        split_path = os.path.join(root_dir, split)

        if not os.path.exists(split_path):
            continue

        for dataset_id in os.listdir(split_path):
            dataset_path = os.path.join(split_path, dataset_id)

            em_dir = os.path.join(dataset_path, "seg")
            if not os.path.isdir(em_dir):
                continue

            for organelle in os.listdir(em_dir):
                organelle_path = os.path.join(em_dir, organelle)

                if not os.path.isdir(organelle_path):
                    continue

                for filename in os.listdir(organelle_path):
                    if filename.endswith(".zst") and filename.startswith("seg_"):
                        full_path = os.path.join(organelle_path, filename)
                        dataset_dict[dataset_id].append(full_path)

    return dict(dataset_dict)


def print_em_info(volume: np.ndarray, dataset_id: str, organelle: str = None):
    """
    Print the information of the EM volume.
    """
    print(f"Dataset ID: {dataset_id}")
    if organelle:
        print(f"Organelle: {organelle}")
    print(f"Shape: {volume.shape}")
    print(f"Number of voxels above 0: {np.sum(volume > 0)}")


def get_normalization_params(dataset_pathes: list):
    """
    Compute the mean and standard deviation of the dataset.
    """
    for dataset_path in dataset_pathes:
        em = Volume(dataset_path)
        em.load()
        volume = em.volume
        print_em_info(volume, dataset_path)


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    root_dir = os.path.join(cfg.datasets_root, "finetune")
    dataset_dict = build_em_dataset_dict(root_dir)
    normalization_params = {}

    for dataset_id, dataset_pathes in tqdm(dataset_dict.items()):
        print(f"Processing dataset: {dataset_id}")
        get_normalization_params(dataset_pathes)


if __name__ == "__main__":
    main()
