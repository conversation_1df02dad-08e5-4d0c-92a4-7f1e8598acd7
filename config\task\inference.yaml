volume_dir: "${datasets_root}/em_s0/single"
masks_dir: "${datasets_root}/mito_seg/single"
# ref_masks_dir: "${datasets_root}/mito_seg/single"
sam2_config_path: "sam2_config/sam2.1_hiera_t.yaml"
sam2_model_path: "${root}/SAM2/checkpoints/sam2.1_hiera_tiny.pt"
output_dir: "${output_root}/mito_val/val"

state: "inference"
switch_model: true
keep_in_mem: true
save_masks: true
mask_to_binary: true
label_start: 400
label_stride: 200
to_instance: true

mean: 0.445
std: 0.269

# UNet configuration
unet_config_path: "unet_config/test_config.yaml"
override_unet_config:
  model_path: "${output_root}/mito_ckpt/few_avg_binary_down1/best_checkpoint.pytorch"
